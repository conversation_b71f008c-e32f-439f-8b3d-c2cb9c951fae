#!/bin/bash

# XYZ Parking API 部署脚本
# 使用方法: ./deploy.sh [your-ec2-ip] [your-key.pem]

set -e

if [ $# -ne 2 ]; then
    echo "使用方法: $0 <EC2-IP> <KEY-FILE>"
    echo "示例: $0 *********** my-key.pem"
    exit 1
fi

EC2_IP=$1
KEY_FILE=$2
EC2_USER="ubuntu"
PROJECT_DIR="/home/<USER>/xyz-parking"

echo "🚀 开始部署 XYZ Parking API 到 EC2..."

# 检查密钥文件权限
chmod 400 $KEY_FILE

# 1. 上传代码文件
echo "📁 上传代码文件..."
scp -i $KEY_FILE -r ../api-server $EC2_USER@$EC2_IP:$PROJECT_DIR/
scp -i $KEY_FILE -r ../database_source_code $EC2_USER@$EC2_IP:$PROJECT_DIR/
scp -i $KEY_FILE .env.production $EC2_USER@$EC2_IP:$PROJECT_DIR/api-server/.env
scp -i $KEY_FILE ecosystem.config.js $EC2_USER@$EC2_IP:$PROJECT_DIR/
scp -i $KEY_FILE nginx.conf $EC2_USER@$EC2_IP:$PROJECT_DIR/

# 2. 在 EC2 上执行部署命令
echo "⚙️ 在 EC2 上执行部署..."
ssh -i $KEY_FILE $EC2_USER@$EC2_IP << 'EOF'
    set -e
    
    # 进入项目目录
    cd /home/<USER>/xyz-parking/api-server
    
    # 安装依赖
    echo "📦 安装 Node.js 依赖..."
    npm install --production
    
    # 重启 PM2 应用
    echo "🔄 重启应用..."
    pm2 delete xyz-parking-api || true
    pm2 start ../ecosystem.config.js
    pm2 save
    
    # 更新 Nginx 配置
    echo "🌐 更新 Nginx 配置..."
    sudo cp ../nginx.conf /etc/nginx/sites-available/xyz-parking
    sudo nginx -t
    sudo systemctl reload nginx
    
    echo "✅ 部署完成!"
    echo "📊 应用状态:"
    pm2 status
    
    echo "🔗 API 健康检查:"
    curl -s http://localhost:3000/api/health || echo "API 未响应"
EOF

echo "🎉 部署完成! 请访问 https://$EC2_IP/api/health 检查 API 状态"
