#!/bin/bash

# EC2 初始环境设置脚本
# 在 EC2 实例上运行此脚本来安装所有必要的软件

set -e

echo "🚀 开始设置 EC2 环境..."

# 更新系统
echo "📦 更新系统包..."
sudo apt update && sudo apt upgrade -y

# 安装 Node.js 18.x
echo "📦 安装 Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 MySQL Server
echo "🗄️ 安装 MySQL Server..."
sudo apt-get install -y mysql-server mysql-client

# 安装 Nginx
echo "🌐 安装 Nginx..."
sudo apt-get install -y nginx

# 安装 Certbot
echo "🔒 安装 Certbot..."
sudo apt-get install -y certbot python3-certbot-nginx

# 安装 PM2
echo "⚙️ 安装 PM2..."
sudo npm install -g pm2

# 创建必要的目录
echo "📁 创建项目目录..."
mkdir -p /home/<USER>/xyz-parking
mkdir -p /home/<USER>/logs

# 配置 MySQL
echo "🗄️ 配置 MySQL..."
sudo mysql_secure_installation

echo "✅ EC2 环境设置完成!"
echo ""
echo "📋 下一步操作:"
echo "1. 配置 MySQL 数据库用户和权限"
echo "2. 上传项目代码"
echo "3. 配置域名和 SSL 证书"
echo "4. 运行部署脚本"
echo ""
echo "💡 MySQL 配置命令:"
echo "sudo mysql -u root -p"
echo "CREATE DATABASE xyz_parking_db;"
echo "CREATE USER 'xyz_parking_user'@'localhost' IDENTIFIED BY 'your_password';"
echo "GRANT ALL PRIVILEGES ON xyz_parking_db.* TO 'xyz_parking_user'@'localhost';"
echo "FLUSH PRIVILEGES;"
